version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: princess_payments
      POSTGRES_USER: princess
      POSTGRES_PASSWORD: princess_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - princess-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - princess-network

  # Core Services
  payment-api:
    build:
      context: ./services/payment-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - REDIS_URL=redis://redis:6379
      - FRAUD_DETECTION_URL=http://fraud-detection:8001
      - TRANSACTION_PROCESSOR_URL=http://transaction-processor:8002
    depends_on:
      - postgres
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/payment-api:/app

  fraud-detection:
    build:
      context: ./services/fraud-detection
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models
    depends_on:
      - postgres
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/fraud-detection:/app
      - fraud_models:/app/models

  transaction-processor:
    build:
      context: ./services/transaction-processor
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/transaction-processor:/app

  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PAYMENT_API_URL=http://payment-api:8000
      - FRAUD_DETECTION_URL=http://fraud-detection:8001
      - TRANSACTION_PROCESSOR_URL=http://transaction-processor:8002
    depends_on:
      - payment-api
      - fraud-detection
      - transaction-processor
    networks:
      - princess-network

networks:
  princess-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  fraud_models:
